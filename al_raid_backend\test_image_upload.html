<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع صور المنتجات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            direction: rtl;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .uploaded-image {
            max-width: 300px;
            margin-top: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>اختبار رفع صور المنتجات</h1>
    
    <form id="productForm" enctype="multipart/form-data">
        <div class="form-group">
            <label for="user_id">معرف المستخدم:</label>
            <input type="number" id="user_id" name="user_id" value="1" required>
        </div>
        
        <div class="form-group">
            <label for="description">وصف المنتج:</label>
            <textarea id="description" name="description" rows="3" placeholder="أدخل وصف المنتج" required></textarea>
        </div>
        
        <div class="form-group">
            <label for="country_of_origin">بلد المنشأ:</label>
            <input type="text" id="country_of_origin" name="country_of_origin" placeholder="مثال: السعودية" required>
        </div>
        
        <div class="form-group">
            <label for="price">السعر:</label>
            <input type="number" id="price" name="price" step="0.01" placeholder="0.00" required>
        </div>
        
        <div class="form-group">
            <label for="type">نوع المنتج:</label>
            <select id="type" name="type" required>
                <option value="industrial">صناعي</option>
                <option value="food">غذائي</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="image">صورة المنتج:</label>
            <input type="file" id="image" name="image" accept="image/*" required>
        </div>
        
        <button type="submit">رفع المنتج</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('productForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            // إظهار رسالة التحميل
            resultDiv.innerHTML = '<div class="result">جاري رفع المنتج...</div>';
            
            try {
                const response = await fetch('https://alraid.ridcod.com/api/products', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                console.log('API Response:', result);
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>تم رفع المنتج بنجاح!</h3>
                            <p>${result.message}</p>
                        </div>
                    `;
                    
                    // اختبار عرض الصور المرفوعة
                    testImageDisplay();
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>خطأ في رفع المنتج</h3>
                            <p>${result.message || 'خطأ غير معروف'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>خطأ في الاتصال</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        });
        
        async function testImageDisplay() {
            try {
                const response = await fetch('https://alraid.ridcod.com/api/products');
                const result = await response.json();
                
                if (result.success && result.products.length > 0) {
                    const lastProduct = result.products[result.products.length - 1];
                    const resultDiv = document.getElementById('result');
                    
                    if (lastProduct.image_url) {
                        resultDiv.innerHTML += `
                            <div class="result success">
                                <h3>اختبار عرض الصورة:</h3>
                                <p>رابط الصورة: <a href="${lastProduct.image_url}" target="_blank">${lastProduct.image_url}</a></p>
                                <img src="${lastProduct.image_url}" alt="صورة المنتج" class="uploaded-image" 
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div style="display:none; padding: 10px; background: #f8d7da; color: #721c24; border-radius: 5px;">
                                    فشل في تحميل الصورة
                                </div>
                            </div>
                        `;
                    }
                }
            } catch (error) {
                console.error('Error testing image display:', error);
            }
        }
    </script>
</body>
</html>
