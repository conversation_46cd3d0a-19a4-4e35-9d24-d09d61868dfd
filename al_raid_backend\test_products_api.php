<?php
// Test API products endpoint
$url = 'https://alraid.ridcod.com/api/products';

echo "<h1>اختبار API المنتجات</h1>";

try {
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Content-Type: application/json',
                'Accept: application/json'
            ]
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>فشل في الاتصال بالـ API</p>";
        exit;
    }
    
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "<p style='color: red;'>خطأ في تحليل JSON: " . json_last_error_msg() . "</p>";
        echo "<pre>$response</pre>";
        exit;
    }
    
    echo "<h2>استجابة API:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
    echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "</pre>";
    
    if (isset($data['success']) && $data['success']) {
        echo "<h2>المنتجات المجدة:</h2>";
        
        if (isset($data['products']) && is_array($data['products'])) {
            foreach ($data['products'] as $index => $product) {
                echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
                echo "<h3>منتج #" . ($index + 1) . "</h3>";
                echo "<p><strong>الوصف:</strong> " . htmlspecialchars($product['description'] ?? 'غير محدد') . "</p>";
                echo "<p><strong>السعر:</strong> " . ($product['price'] ?? 'غير محدد') . "</p>";
                echo "<p><strong>النوع:</strong> " . htmlspecialchars($product['type'] ?? 'غير محدد') . "</p>";
                
                if (isset($product['image']) && !empty($product['image'])) {
                    echo "<p><strong>اسم ملف الصورة:</strong> " . htmlspecialchars($product['image']) . "</p>";
                }
                
                if (isset($product['image_url']) && !empty($product['image_url'])) {
                    echo "<p><strong>رابط الصورة:</strong> <a href='" . htmlspecialchars($product['image_url']) . "' target='_blank'>" . htmlspecialchars($product['image_url']) . "</a></p>";
                    echo "<div style='margin: 10px 0;'>";
                    echo "<img src='" . htmlspecialchars($product['image_url']) . "' alt='صورة المنتج' style='max-width: 200px; max-height: 200px; border: 1px solid #ddd;' ";
                    echo "onerror=\"this.style.display='none'; this.nextElementSibling.style.display='block';\">";
                    echo "<div style='display:none; padding: 10px; background: #f8d7da; color: #721c24; border-radius: 5px;'>فشل في تحميل الصورة</div>";
                    echo "</div>";
                }
                
                if (isset($product['images']) && is_array($product['images'])) {
                    echo "<p><strong>مصفوفة الصور:</strong></p>";
                    echo "<ul>";
                    foreach ($product['images'] as $imageUrl) {
                        echo "<li><a href='" . htmlspecialchars($imageUrl) . "' target='_blank'>" . htmlspecialchars($imageUrl) . "</a></li>";
                    }
                    echo "</ul>";
                }
                
                echo "</div>";
            }
        } else {
            echo "<p>لا توجد منتجات</p>";
        }
    } else {
        echo "<p style='color: red;'>فشل في تحميل المنتجات: " . ($data['message'] ?? 'خطأ غير معروف') . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>

<hr>
<h2>اختبار الوصول المباشر لمجلد الصور</h2>
<p>اختبار الوصول المباشر لمجلد الرفع:</p>
<ul>
    <li><a href="https://alraid.ridcod.com/uploads/products/" target="_blank">مجلد الصور</a></li>
    <li><a href="https://alraid.ridcod.com/uploads/" target="_blank">مجلد الرفع الرئيسي</a></li>
</ul>
