<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Upload Fix - ALraid</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1976D2;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #1976D2;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #1565C0;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .supported-formats {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Test Image Upload Fix</h1>
        
        <div class="supported-formats">
            <h3>✅ Now Supported Image Formats:</h3>
            <ul>
                <li><strong>JPG/JPEG</strong> - Standard photo format</li>
                <li><strong>PNG</strong> - Transparent images</li>
                <li><strong>GIF</strong> - Animated images</li>
                <li><strong>WebP</strong> - Modern web format</li>
                <li><strong>BMP</strong> - Bitmap images</li>
            </ul>
            <p><strong>Maximum file size:</strong> 5MB</p>
        </div>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="test_type">Test Type:</label>
                <select id="test_type" name="test_type" required>
                    <option value="">Select test type...</option>
                    <option value="commercial_register">Commercial Register Photo (Merchant Registration)</option>
                    <option value="product_image">Product Image Upload</option>
                </select>
            </div>

            <div class="form-group">
                <label for="image_file">Select Image File:</label>
                <input type="file" id="image_file" name="image_file" accept="image/*" required>
                <small style="color: #666;">Try different formats: JPG, PNG, GIF, WebP, BMP</small>
            </div>

            <div class="form-group">
                <label for="description">Test Description (for product images):</label>
                <textarea id="description" name="description" rows="3" placeholder="Enter product description..."></textarea>
            </div>

            <button type="submit">🚀 Test Upload</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const testType = document.getElementById('test_type').value;
            const imageFile = document.getElementById('image_file').files[0];
            const description = document.getElementById('description').value;
            
            if (!imageFile) {
                showResult('Please select an image file', 'error');
                return;
            }
            
            // Show file info
            showResult(`Testing upload of: ${imageFile.name} (${imageFile.type}, ${(imageFile.size/1024/1024).toFixed(2)}MB)`, 'success');
            
            if (testType === 'commercial_register') {
                // Test commercial register upload
                formData.append('commercial_register_photo', imageFile);
                formData.append('full_name', 'Test Merchant');
                formData.append('email', '<EMAIL>');
                formData.append('phone_number', '+1234567890');
                formData.append('password', 'Test123!@#');
                formData.append('shipping_address', 'Test Address');
                formData.append('country', 'Test Country');
                formData.append('role', 'merchant');
                
                try {
                    const response = await fetch('/ALraid/al_raid_backend/api/register', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        showResult('✅ Commercial register upload successful!', 'success');
                    } else {
                        showResult('❌ Upload failed: ' + result.message, 'error');
                    }
                } catch (error) {
                    showResult('❌ Network error: ' + error.message, 'error');
                }
                
            } else if (testType === 'product_image') {
                // Test product image upload
                formData.append('image', imageFile);
                formData.append('description', description || 'Test product description');
                formData.append('country_of_origin', 'Test Country');
                formData.append('price', '99.99');
                formData.append('type', 'industrial');
                formData.append('user_id', '1'); // Assuming user ID 1 exists
                
                try {
                    const response = await fetch('/ALraid/al_raid_backend/api/products', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        showResult('✅ Product image upload successful!', 'success');
                    } else {
                        showResult('❌ Upload failed: ' + result.message, 'error');
                    }
                } catch (error) {
                    showResult('❌ Network error: ' + error.message, 'error');
                }
            }
        });
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
