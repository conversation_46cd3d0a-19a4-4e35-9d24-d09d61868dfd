import 'package:flutter/material.dart';

class ThemeProvider with ChangeNotifier {
  // Always use light theme mode
  ThemeMode get themeMode => ThemeMode.light;

  // Always return false for dark mode since we only support light mode
  bool get isDarkMode => false;
  bool get isLightMode => true;

  // No-op methods to maintain compatibility with existing code
  void setThemeMode(ThemeMode themeMode) {
    // Do nothing - always stay in light mode
  }

  void toggleTheme() {
    // Do nothing - always stay in light mode
  }

  void loadThemeMode() async {
    // Do nothing - always stay in light mode
  }
}
