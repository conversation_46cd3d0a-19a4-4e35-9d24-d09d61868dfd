// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'الرائد';

  @override
  String get appSubtitle => 'منصة المنتجات الصناعية والغذائية';

  @override
  String get welcome => 'مرحباً';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'إنشاء حساب';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get firstName => 'الاسم الأول';

  @override
  String get lastName => 'اسم العائلة';

  @override
  String get phone => 'رقم الهاتف';

  @override
  String get userType => 'نوع المستخدم';

  @override
  String get buyer => 'مشتري';

  @override
  String get merchant => 'تاجر';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟';

  @override
  String get termsAndConditions => 'الشروط والأحكام';

  @override
  String get termsText =>
      'باستخدامك لهذا التطبيق، فإنك توافق على شروطنا وأحكامنا. يرجى قراءتها بعناية قبل المتابعة.';

  @override
  String get acceptTerms => 'أوافق على الشروط والأحكام';

  @override
  String get continueButton => 'متابعة';

  @override
  String get home => 'الرئيسية';

  @override
  String get products => 'المنتجات';

  @override
  String get orders => 'الطلبات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get search => 'بحث';

  @override
  String get searchProducts => 'البحث عن المنتجات...';

  @override
  String get categories => 'الفئات';

  @override
  String get allCategories => 'جميع الفئات';

  @override
  String get industrial => 'صناعي';

  @override
  String get food => 'غذائي';

  @override
  String get electronics => 'إلكترونيات';

  @override
  String get automotive => 'سيارات';

  @override
  String get textiles => 'منسوجات';

  @override
  String get chemicals => 'كيماويات';

  @override
  String get machinery => 'آلات';

  @override
  String get agriculture => 'زراعة';

  @override
  String get featuredProducts => 'المنتجات المميزة';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get productDetails => 'تفاصيل المنتج';

  @override
  String get specifications => 'المواصفات';

  @override
  String get description => 'الوصف';

  @override
  String get price => 'السعر';

  @override
  String get quantity => 'الكمية';

  @override
  String get addToCart => 'أضف للسلة';

  @override
  String get buyNow => 'اشتري الآن';

  @override
  String get addProduct => 'إضافة منتج';

  @override
  String get productName => 'اسم المنتج';

  @override
  String get productDescription => 'وصف المنتج';

  @override
  String get productPrice => 'سعر المنتج';

  @override
  String get productCategory => 'فئة المنتج';

  @override
  String get productImages => 'صور المنتج';

  @override
  String get addImages => 'إضافة صور';

  @override
  String get selectCategory => 'اختر الفئة';

  @override
  String get saveProduct => 'حفظ المنتج';

  @override
  String get myProfile => 'ملفي الشخصي';

  @override
  String get editProfile => 'تعديل الملف';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get language => 'اللغة';

  @override
  String get theme => 'المظهر';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get confirm => 'تأكيد';

  @override
  String get success => 'نجح';

  @override
  String get error => 'خطأ';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get noProductsFound => 'لم يتم العثور على منتجات';

  @override
  String get noOrdersFound => 'لم يتم العثور على طلبات';

  @override
  String get orderHistory => 'تاريخ الطلبات';

  @override
  String get orderDetails => 'تفاصيل الطلب';

  @override
  String get orderStatus => 'حالة الطلب';

  @override
  String get pending => 'قيد الانتظار';

  @override
  String get processing => 'قيد المعالجة';

  @override
  String get shipped => 'تم الشحن';

  @override
  String get delivered => 'تم التسليم';

  @override
  String get cancelled => 'ملغي';

  @override
  String get orderDate => 'تاريخ الطلب';

  @override
  String get orderTotal => 'إجمالي الطلب';

  @override
  String get contactSeller => 'اتصل بالبائع';

  @override
  String get trackOrder => 'تتبع الطلب';

  @override
  String get myProducts => 'منتجاتي';

  @override
  String get manageOrders => 'إدارة الطلبات';

  @override
  String get addNewProduct => 'إضافة منتج جديد';

  @override
  String get editProduct => 'تعديل المنتج';

  @override
  String get deleteProduct => 'حذف المنتج';

  @override
  String get productAdded => 'تم إضافة المنتج بنجاح';

  @override
  String get productUpdated => 'تم تحديث المنتج بنجاح';

  @override
  String get productDeleted => 'تم حذف المنتج بنجاح';

  @override
  String get orderPlaced => 'تم إرسال الطلب بنجاح';

  @override
  String get profileUpdated => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String get passwordChanged => 'تم تغيير كلمة المرور بنجاح';

  @override
  String get loggedOut => 'تم تسجيل الخروج بنجاح';

  @override
  String get invalidCredentials => 'البريد الإلكتروني أو كلمة المرور غير صحيحة';

  @override
  String get registrationFailed => 'فشل في إنشاء الحساب';

  @override
  String get networkError => 'خطأ في الشبكة. يرجى المحاولة مرة أخرى.';

  @override
  String get requiredField => 'هذا الحقل مطلوب';

  @override
  String get invalidEmail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get passwordTooShort => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';

  @override
  String get passwordsNotMatch => 'كلمات المرور غير متطابقة';

  @override
  String get invalidPhone => 'يرجى إدخال رقم هاتف صحيح';

  @override
  String get sar => 'ريال';

  @override
  String get currency => 'ريال';

  @override
  String get dark => 'داكن';

  @override
  String get light => 'فاتح';

  @override
  String get system => 'النظام';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'English';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get selectTheme => 'اختر المظهر';

  @override
  String get all => 'الكل';

  @override
  String get approved => 'مقبول';

  @override
  String get rejected => 'مرفوض';

  @override
  String get filterByStatus => 'تصفية حسب الحالة';

  @override
  String get noProductsForStatus => 'لا توجد منتجات لهذه الحالة';

  @override
  String get productType => 'نوع المنتج';

  @override
  String get underReview => 'قيد المراجعة';

  @override
  String get change => 'تغيير';
}
