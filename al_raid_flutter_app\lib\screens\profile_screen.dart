import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

import '../providers/auth_provider.dart';
import '../providers/language_provider.dart';
import '../widgets/glassmorphic_card.dart';
import '../widgets/gradient_button.dart';
import '../widgets/animated_input_field.dart';
import '../widgets/glassmorphic_alert_dialog.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isEditing = false;
  bool _isChangingPassword = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _loadUserData();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.user;

    if (user != null) {
      _nameController.text = user.fullName;
      _emailController.text = user.email;
      _phoneController.text = user.phoneNumber;
    }
  }

  Future<void> _updateProfile() async {
    if (_formKey.currentState?.validate() ?? false) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final languageProvider =
          Provider.of<LanguageProvider>(context, listen: false);
      try {
        await authProvider.updateProfile(
          fullName: _nameController.text.trim(),
          phoneNumber: _phoneController.text.trim(),
          shippingAddress: '', // يمكن إضافة حقل لاحقاً
          country: 'Unknown', // يمكن إضافة حقل لاحقاً
        );

        setState(() {
          _isEditing = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                languageProvider.isArabic
                    ? 'تم تحديث الملف الشخصي بنجاح'
                    : 'Profile updated successfully',
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                languageProvider.isArabic
                    ? 'خطأ في تحديث الملف الشخصي: ${e.toString()}'
                    : 'Error updating profile: ${e.toString()}',
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      }
    }
  }

  Future<void> _changePassword() async {
    final languageProvider =
        Provider.of<LanguageProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // Validate form first
    if (!_validatePasswordForm()) {
      return;
    }

    if (_newPasswordController.text != _confirmPasswordController.text) {
      await GlassmorphicAlertDialog.show(
        context: context,
        title: languageProvider.isArabic ? 'خطأ' : 'Error',
        message: languageProvider.isArabic
            ? 'كلمات المرور غير متطابقة'
            : 'Passwords do not match',
        isSuccess: false,
      );
      return;
    }

    try {
      final success = await authProvider.changePassword(
        currentPassword: _currentPasswordController.text,
        newPassword: _newPasswordController.text,
      );

      if (success && mounted) {
        setState(() {
          _isChangingPassword = false;
          _currentPasswordController.clear();
          _newPasswordController.clear();
          _confirmPasswordController.clear();
        });

        await GlassmorphicAlertDialog.show(
          context: context,
          title: languageProvider.isArabic ? 'نجح' : 'Success',
          message: languageProvider.isArabic
              ? 'تم تغيير كلمة المرور بنجاح'
              : 'Password changed successfully',
          isSuccess: true,
          icon: Icons.check_circle,
        );
      } else if (mounted) {
        await GlassmorphicAlertDialog.show(
          context: context,
          title: languageProvider.isArabic ? 'خطأ' : 'Error',
          message: authProvider.error ??
              (languageProvider.isArabic
                  ? 'فشل في تغيير كلمة المرور'
                  : 'Failed to change password'),
          isSuccess: false,
          icon: Icons.error,
        );
      }
    } catch (e) {
      if (mounted) {
        await GlassmorphicAlertDialog.show(
          context: context,
          title: languageProvider.isArabic ? 'خطأ' : 'Error',
          message: languageProvider.isArabic
              ? 'خطأ في تغيير كلمة المرور: ${e.toString()}'
              : 'Error changing password: ${e.toString()}',
          isSuccess: false,
          icon: Icons.error,
        );
      }
    }
  }

  bool _validatePasswordForm() {
    final currentPassword = _currentPasswordController.text;
    final newPassword = _newPasswordController.text;
    final confirmPassword = _confirmPasswordController.text;
    final languageProvider =
        Provider.of<LanguageProvider>(context, listen: false);

    if (currentPassword.isEmpty) {
      GlassmorphicAlertDialog.show(
        context: context,
        title: languageProvider.isArabic ? 'خطأ' : 'Error',
        message: languageProvider.isArabic
            ? 'يرجى إدخال كلمة المرور الحالية'
            : 'Please enter current password',
        isSuccess: false,
      );
      return false;
    }

    if (newPassword.isEmpty) {
      GlassmorphicAlertDialog.show(
        context: context,
        title: languageProvider.isArabic ? 'خطأ' : 'Error',
        message: languageProvider.isArabic
            ? 'يرجى إدخال كلمة المرور الجديدة'
            : 'Please enter new password',
        isSuccess: false,
      );
      return false;
    }

    if (newPassword.length < 8) {
      GlassmorphicAlertDialog.show(
        context: context,
        title: languageProvider.isArabic ? 'خطأ' : 'Error',
        message: languageProvider.isArabic
            ? 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
            : 'Password must be at least 8 characters',
        isSuccess: false,
      );
      return false;
    }

    if (!RegExp(r'[0-9]').hasMatch(newPassword)) {
      GlassmorphicAlertDialog.show(
        context: context,
        title: languageProvider.isArabic ? 'خطأ' : 'Error',
        message: languageProvider.isArabic
            ? 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل'
            : 'Password must contain at least one number',
        isSuccess: false,
      );
      return false;
    }

    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(newPassword)) {
      GlassmorphicAlertDialog.show(
        context: context,
        title: languageProvider.isArabic ? 'خطأ' : 'Error',
        message: languageProvider.isArabic
            ? 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل'
            : 'Password must contain at least one special character',
        isSuccess: false,
      );
      return false;
    }

    if (confirmPassword.isEmpty) {
      GlassmorphicAlertDialog.show(
        context: context,
        title: languageProvider.isArabic ? 'خطأ' : 'Error',
        message: languageProvider.isArabic
            ? 'يرجى تأكيد كلمة المرور'
            : 'Please confirm password',
        isSuccess: false,
      );
      return false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isRTL = languageProvider.isArabic;
    final authProvider = Provider.of<AuthProvider>(context);
    final user = authProvider.user;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5), // Light mode only
      appBar: AppBar(
        title: Text(
          isRTL ? 'الملف الشخصي' : 'Profile',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: theme.primaryColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(
            isRTL ? Icons.arrow_forward : Icons.arrow_back,
            color: Colors.white,
          ),
        ),
        actions: [
          if (!_isEditing && !_isChangingPassword)
            IconButton(
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              icon: const Icon(
                Icons.edit,
                color: Colors.white,
              ),
            ),
        ],
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _animationController,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Profile Header
                  GlassmorphicCard(
                    child: Column(
                      children: [
                        // Avatar
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                theme.primaryColor,
                                theme.primaryColor.withOpacity(0.7),
                              ],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: theme.primaryColor.withOpacity(0.3),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: Icon(
                            user?.userType == 'merchant'
                                ? Icons.store
                                : Icons.person,
                            size: 50,
                            color: Colors.white,
                          ),
                        ),

                        const SizedBox(height: 16),

                        Text(
                          user?.name ?? '',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87, // Light mode only
                          ),
                        ),

                        const SizedBox(height: 8),

                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: theme.primaryColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            user?.userType == 'merchant'
                                ? (isRTL ? 'تاجر' : 'Merchant')
                                : (isRTL ? 'مشتري' : 'Buyer'),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: theme.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                      .animate()
                      .fadeIn(delay: 200.ms, duration: 600.ms)
                      .slideY(begin: 0.2, end: 0),

                  const SizedBox(height: 20),

                  // Profile Information
                  GlassmorphicCard(
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                isRTL
                                    ? 'المعلومات الشخصية'
                                    : 'Personal Information',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87, // Light mode only
                                ),
                              ),
                              if (_isEditing)
                                Row(
                                  children: [
                                    TextButton(
                                      onPressed: () {
                                        setState(() {
                                          _isEditing = false;
                                          _loadUserData();
                                        });
                                      },
                                      child: Text(
                                        isRTL ? 'إلغاء' : 'Cancel',
                                        style: TextStyle(
                                            color: Colors.grey.shade600),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Consumer<AuthProvider>(
                                      builder: (context, authProvider, child) {
                                        return GradientButton(
                                          text: isRTL ? 'حفظ' : 'Save',
                                          onPressed: _updateProfile,
                                          isLoading: authProvider.isLoading,
                                          height: 40,
                                          icon: const Icon(
                                            Icons.save,
                                            color: Colors.white,
                                            size: 16,
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                            ],
                          ),
                          const SizedBox(height: 20),
                          AnimatedInputField(
                            labelText: isRTL ? 'الاسم الكامل' : 'Full Name',
                            controller: _nameController,
                            enabled: _isEditing,
                            prefixIcon: const Icon(Icons.person_outline),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return isRTL
                                    ? 'يرجى إدخال الاسم'
                                    : 'Please enter name';
                              }
                              return null;
                            },
                          ),
                          AnimatedInputField(
                            labelText: isRTL ? 'البريد الإلكتروني' : 'Email',
                            controller: _emailController,
                            enabled: _isEditing,
                            keyboardType: TextInputType.emailAddress,
                            prefixIcon: const Icon(Icons.email_outlined),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return isRTL
                                    ? 'يرجى إدخال البريد الإلكتروني'
                                    : 'Please enter email';
                              }
                              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                  .hasMatch(value!)) {
                                return isRTL
                                    ? 'يرجى إدخال بريد إلكتروني صحيح'
                                    : 'Please enter valid email';
                              }
                              return null;
                            },
                          ),
                          AnimatedInputField(
                            labelText: isRTL ? 'رقم الهاتف' : 'Phone Number',
                            controller: _phoneController,
                            enabled: _isEditing,
                            keyboardType: TextInputType.phone,
                            prefixIcon: const Icon(Icons.phone_outlined),
                          ),
                          if (!_isEditing && !_isChangingPassword) ...[
                            const SizedBox(height: 20),
                            GradientButton(
                              text: isRTL
                                  ? 'تغيير كلمة المرور'
                                  : 'Change Password',
                              onPressed: () {
                                setState(() {
                                  _isChangingPassword = true;
                                });
                              },
                              width: double.infinity,
                              isOutlined: true,
                              icon: Icon(
                                Icons.lock_outline,
                                color: theme.primaryColor,
                                size: 20,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  )
                      .animate()
                      .fadeIn(delay: 400.ms, duration: 600.ms)
                      .slideY(begin: 0.2, end: 0),

                  // Change Password Section
                  if (_isChangingPassword) ...[
                    const SizedBox(height: 20),
                    GlassmorphicCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                isRTL ? 'تغيير كلمة المرور' : 'Change Password',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87, // Light mode only
                                ),
                              ),
                              Wrap(
                                alignment: WrapAlignment.end,
                                spacing: 8,
                                children: [
                                  TextButton(
                                    onPressed: () {
                                      setState(() {
                                        _isChangingPassword = false;
                                        _currentPasswordController.clear();
                                        _newPasswordController.clear();
                                        _confirmPasswordController.clear();
                                      });
                                    },
                                    child: Text(
                                      isRTL ? 'إلغاء' : 'Cancel',
                                      style: TextStyle(
                                          color: Colors.grey.shade600),
                                    ),
                                  ),
                                  Consumer<AuthProvider>(
                                    builder: (context, authProvider, child) {
                                      return GradientButton(
                                        text: isRTL ? 'حفظ' : 'Save',
                                        onPressed: _changePassword,
                                        isLoading: authProvider.isLoading,
                                        height: 44,
                                        width: 120,
                                        icon: authProvider.isLoading
                                            ? null
                                            : const Icon(
                                                Icons.save,
                                                color: Colors.white,
                                                size: 18,
                                              ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),
                          AnimatedInputField(
                            labelText: isRTL
                                ? 'كلمة المرور الحالية'
                                : 'Current Password',
                            controller: _currentPasswordController,
                            obscureText: true,
                            prefixIcon: const Icon(Icons.lock_outline),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return isRTL
                                    ? 'يرجى إدخال كلمة المرور الحالية'
                                    : 'Please enter current password';
                              }
                              return null;
                            },
                          ),
                          AnimatedInputField(
                            labelText:
                                isRTL ? 'كلمة المرور الجديدة' : 'New Password',
                            controller: _newPasswordController,
                            obscureText: true,
                            prefixIcon: const Icon(Icons.lock),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return isRTL
                                    ? 'يرجى إدخال كلمة المرور الجديدة'
                                    : 'Please enter new password';
                              }
                              if (value!.length < 8) {
                                return isRTL
                                    ? 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
                                    : 'Password must be at least 8 characters';
                              }
                              if (!RegExp(r'[0-9]').hasMatch(value)) {
                                return isRTL
                                    ? 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل'
                                    : 'Password must contain at least one number';
                              }
                              if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]')
                                  .hasMatch(value)) {
                                return isRTL
                                    ? 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل'
                                    : 'Password must contain at least one special character';
                              }
                              return null;
                            },
                          ),
                          AnimatedInputField(
                            labelText: isRTL
                                ? 'تأكيد كلمة المرور الجديدة'
                                : 'Confirm New Password',
                            controller: _confirmPasswordController,
                            obscureText: true,
                            prefixIcon: const Icon(Icons.lock),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return isRTL
                                    ? 'يرجى تأكيد كلمة المرور'
                                    : 'Please confirm password';
                              }
                              if (value != _newPasswordController.text) {
                                return isRTL
                                    ? 'كلمات المرور غير متطابقة'
                                    : 'Passwords do not match';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    )
                        .animate()
                        .fadeIn(delay: 600.ms, duration: 600.ms)
                        .slideY(begin: 0.2, end: 0),
                  ],

                  const SizedBox(height: 20),

                  // Settings
                  GlassmorphicCard(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isRTL ? 'الإعدادات' : 'Settings',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87, // Light mode only
                          ),
                        ),
                        const SizedBox(height: 20),

                        const Divider(),

                        // Language Toggle
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          leading: Icon(
                            Icons.language,
                            color: theme.primaryColor,
                          ),
                          title: Text(
                            isRTL ? 'اللغة العربية' : 'Arabic Language',
                            style: const TextStyle(
                              color: Colors.black87, // Light mode only
                            ),
                          ),
                          trailing: Switch(
                            value: languageProvider.isArabic,
                            onChanged: (value) {
                              languageProvider.toggleLanguage();
                            },
                            activeColor: theme.primaryColor,
                          ),
                        ),

                        const Divider(),

                        // Logout
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          leading: const Icon(
                            Icons.logout,
                            color: Colors.red,
                          ),
                          title: Text(
                            isRTL ? 'تسجيل الخروج' : 'Logout',
                            style: const TextStyle(
                              color: Colors.red,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text(isRTL ? 'تسجيل الخروج' : 'Logout'),
                                content: Text(
                                  isRTL
                                      ? 'هل أنت متأكد من تسجيل الخروج؟'
                                      : 'Are you sure you want to logout?',
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    child: Text(isRTL ? 'إلغاء' : 'Cancel'),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                      authProvider.logout(context);
                                    },
                                    child: Text(
                                      isRTL ? 'تسجيل الخروج' : 'Logout',
                                      style: const TextStyle(color: Colors.red),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  )
                      .animate()
                      .fadeIn(delay: 800.ms, duration: 600.ms)
                      .slideY(begin: 0.2, end: 0),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
