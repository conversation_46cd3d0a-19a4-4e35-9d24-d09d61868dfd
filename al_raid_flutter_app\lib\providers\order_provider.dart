import 'package:flutter/material.dart';
import '../models/order.dart';
import '../services/api_service.dart';

class OrderProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;

  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get error => _error;

  final ApiService _apiService = ApiService();

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // دالة آمنة لتحويل السعر
  double _parsePrice(dynamic priceValue) {
    if (priceValue == null) return 0.0;
    if (priceValue is double) return priceValue;
    if (priceValue is int) return priceValue.toDouble();
    if (priceValue is String) {
      return double.tryParse(priceValue) ?? 0.0;
    }
    return 0.0;
  }

  // إنشاء طلب جديد
  Future<bool> createOrder({
    required int productId,
    required int buyerId,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.createOrder(
        productId: productId,
        buyerId: buyerId,
      );

      if (result['success'] == true) {
        // إعادة تحميل الطلبات لتشمل الطلب الجديد
        await loadUserOrders(buyerId);
        _setLoading(false);
        return true;
      } else {
        _setError(result['message'] ?? 'Failed to create order');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  // تحميل طلبات المستخدم
  Future<void> loadUserOrders(int userId) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.getUserOrders(userId: userId);

      if (result['success'] == true) {
        final ordersData = result['orders'] as List;
        _orders = ordersData.map((orderData) {
          try {
            return Order.fromMap(orderData);
          } catch (e) {
            print('OrderProvider: Error parsing order: $e');
            // إرجاع طلب افتراضي في حالة الخطأ
            return Order(
              id: orderData['id'] ?? 0,
              productId: orderData['product_id'] ?? 0,
              buyerId: orderData['buyer_id'] ?? 0,
              status: orderData['status'] ?? 'under_review',
              createdAt: DateTime.now(),
              productDescription:
                  orderData['product_description'] ?? 'Unknown Product',
              price: _parsePrice(orderData['price']),
              productImage: orderData['product_image'],
              merchantName: orderData['merchant_name'],
            );
          }
        }).toList();

        _setLoading(false);
      } else {
        _setError(result['message'] ?? 'Failed to load orders');
        _setLoading(false);
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
    }
  }

  // تحميل طلبات التاجر
  Future<void> loadMerchantOrders(int merchantId) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.getUserOrders(merchantId: merchantId);

      if (result['success'] == true) {
        final ordersData = result['orders'] as List;
        _orders = ordersData.map((orderData) {
          try {
            return Order.fromMap(orderData);
          } catch (e) {
            print('OrderProvider: Error parsing order: $e');
            return Order(
              id: orderData['id'] ?? 0,
              productId: orderData['product_id'] ?? 0,
              buyerId: orderData['buyer_id'] ?? 0,
              status: orderData['status'] ?? 'under_review',
              createdAt: DateTime.now(),
              productDescription:
                  orderData['product_description'] ?? 'Unknown Product',
              price: _parsePrice(orderData['price']),
              productImage: orderData['product_image'],
              buyerName: orderData['buyer_name'],
              buyerEmail: orderData['buyer_email'],
              buyerPhone: orderData['buyer_phone'],
            );
          }
        }).toList();

        _setLoading(false);
      } else {
        _setError(result['message'] ?? 'Failed to load orders');
        _setLoading(false);
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // إلغاء الطلب (فقط للطلبات تحت المراجعة)
  Future<bool> cancelOrder(int orderId) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.cancelOrder(orderId);

      if (result['success'] == true) {
        // إزالة الطلب من القائمة المحلية
        _orders.removeWhere((order) => order.id == orderId);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? 'Failed to cancel order');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> refreshOrders(int userId, {bool isMerchant = false}) async {
    if (isMerchant) {
      await loadMerchantOrders(userId);
    } else {
      await loadUserOrders(userId);
    }
  }

  // تحميل الطلبات المعتمدة للتاجر فقط
  Future<void> loadApprovedMerchantOrders(int merchantId) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _apiService.getUserOrders(merchantId: merchantId);

      if (result['success'] == true) {
        final ordersData = result['orders'] as List;
        _orders = ordersData
            .map((orderData) {
              try {
                return Order.fromMap(orderData);
              } catch (e) {
                print('OrderProvider: Error parsing order: $e');
                // إرجاع طلب افتراضي في حالة الخطأ
                return Order(
                  id: orderData['id'] ?? 0,
                  productId: orderData['product_id'] ?? 0,
                  buyerId: orderData['buyer_id'] ?? 0,
                  status: orderData['status'] ?? 'under_review',
                  createdAt: DateTime.now(),
                  productDescription:
                      orderData['product_description'] ?? 'Unknown Product',
                  price: _parsePrice(orderData['price']),
                  productImage: orderData['product_image'],
                  buyerName: orderData['buyer_name'],
                  buyerEmail: orderData['buyer_email'],
                  buyerPhone: orderData['buyer_phone'],
                );
              }
            })
            .where((order) => order.isApproved)
            .toList(); // فلترة الطلبات المعتمدة فقط

        _setLoading(false);
        notifyListeners();
      } else {
        _setError(result['message'] ?? 'Failed to load approved orders');
        _setLoading(false);
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
    }
  }

  // إحصائيات الطلبات
  int get totalOrders => _orders.length;
  int get pendingOrdersCount =>
      _orders.where((o) => o.status == 'under_review').length;
  int get approvedOrdersCount =>
      _orders.where((o) => o.status == 'approved').length;
  int get rejectedOrdersCount =>
      _orders.where((o) => o.status == 'rejected').length;

  // فلترة الطلبات حسب الحالة
  List<Order> getOrdersByStatus(String status) {
    return _orders.where((order) => order.status == status).toList();
  }
}
