import 'package:flutter/material.dart';

class ImageUtils {
  static const String baseImageUrl =
      'https://alraid.ridcod.com/uploads/products/';

  /// التحقق من صحة رابط الصورة وإرجاع الرابط الكامل
  static String getFullImageUrl(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return '';
    }

    // إذا كان الرابط كاملاً بالفعل
    if (imagePath.startsWith('http')) {
      return imagePath;
    }

    // إضافة الرابط الأساسي
    return '$baseImageUrl$imagePath';
  }

  /// بناء widget للصورة مع معالجة الأخطاء
  static Widget buildNetworkImage(
    String? imageUrl, {
    double? width,
    double? height,
    BoxFit? fit,
    BorderRadius? borderRadius,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    final fullUrl = getFullImageUrl(imageUrl);

    if (fullUrl.isEmpty) {
      return errorWidget ?? _buildDefaultErrorWidget();
    }

    Widget imageWidget = Image.network(
      fullUrl,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;

        return placeholder ??
            Container(
              width: width,
              height: height,
              color: Colors.grey.shade200,
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                  strokeWidth: 2,
                ),
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        print('Failed to load image: $fullUrl');
        print('Error: $error');
        return errorWidget ??
            _buildDefaultErrorWidget(width: width, height: height);
      },
    );

    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  static Widget _buildDefaultErrorWidget({double? width, double? height}) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey.shade300,
      child: Icon(
        Icons.image_not_supported,
        size: 48,
        color: Colors.grey.shade500,
      ),
    );
  }

  /// بناء carousel للصور المتعددة
  static Widget buildImageCarousel(
    List<String>? images, {
    required double height,
    Function(int)? onPageChanged,
    PageController? controller,
  }) {
    if (images == null || images.isEmpty) {
      return _buildDefaultErrorWidget(height: height);
    }

    if (images.length == 1) {
      return buildNetworkImage(
        images.first,
        height: height,
        fit: BoxFit.cover,
      );
    }

    return PageView.builder(
      controller: controller,
      onPageChanged: onPageChanged,
      itemCount: images.length,
      itemBuilder: (context, index) {
        return buildNetworkImage(
          images[index],
          height: height,
          fit: BoxFit.cover,
        );
      },
    );
  }
}
