<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Commercial Register Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Commercial Register Upload Test</h1>
        <p>Test the merchant registration with commercial register image upload functionality.</p>
        
        <form id="registrationForm">
            <div class="form-group">
                <label for="fullName">Full Name:</label>
                <input type="text" id="fullName" name="full_name" value="Test Merchant" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="phoneNumber">Phone Number:</label>
                <input type="tel" id="phoneNumber" name="phone_number" value="+1234567890" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="Test123!@#" required>
            </div>
            
            <div class="form-group">
                <label for="shippingAddress">Shipping Address:</label>
                <textarea id="shippingAddress" name="shipping_address" rows="3" required>123 Test Street, Test City, Test Country</textarea>
            </div>
            
            <div class="form-group">
                <label for="country">Country:</label>
                <input type="text" id="country" name="country" value="Test Country" required>
            </div>
            
            <div class="form-group">
                <label for="role">Role:</label>
                <select id="role" name="role" required>
                    <option value="merchant" selected>Merchant</option>
                    <option value="buyer">Buyer</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="commercialRegister">Commercial Register Photo:</label>
                <input type="file" id="commercialRegister" name="commercial_register_photo" accept="image/*" required>
                <div id="fileInfo" class="file-info" style="display: none;"></div>
            </div>
            
            <button type="submit">🚀 Test Registration</button>
            <button type="button" onclick="clearResult()">🧹 Clear Result</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        const form = document.getElementById('registrationForm');
        const fileInput = document.getElementById('commercialRegister');
        const fileInfo = document.getElementById('fileInfo');
        const resultDiv = document.getElementById('result');

        // Show file information when selected
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                fileInfo.style.display = 'block';
                fileInfo.innerHTML = `
                    <strong>Selected File:</strong><br>
                    Name: ${file.name}<br>
                    Size: ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                    Type: ${file.type}<br>
                    Last Modified: ${new Date(file.lastModified).toLocaleString()}
                `;
            } else {
                fileInfo.style.display = 'none';
            }
        });

        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('❌ Please select a commercial register image file.', 'error');
                return;
            }
            
            // Show file info
            showResult(`Testing registration with commercial register upload...\n\nFile: ${file.name} (${file.type}, ${(file.size/1024/1024).toFixed(2)}MB)`, 'info');
            
            try {
                const response = await fetch('./api/register', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                console.log('Registration Response:', result);
                
                if (result.success) {
                    showResult(`✅ Registration successful!\n\nMessage: ${result.message}\nUser ID: ${result.user.id}\nEmail: ${result.user.email}\nRole: ${result.user.role}\nStatus: ${result.user.status}\nCommercial Register: ${result.user.commercial_register_photo || 'Not uploaded'}`, 'success');
                } else {
                    showResult(`❌ Registration failed!\n\nError: ${result.message}`, 'error');
                }
            } catch (error) {
                console.error('Network Error:', error);
                showResult(`❌ Network error!\n\nError: ${error.message}`, 'error');
            }
        });

        function showResult(message, type) {
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function clearResult() {
            resultDiv.innerHTML = '';
        }

        // Auto-generate unique email to avoid conflicts
        document.getElementById('email').value = `testmerchant${Date.now()}@example.com`;
    </script>
</body>
</html>
