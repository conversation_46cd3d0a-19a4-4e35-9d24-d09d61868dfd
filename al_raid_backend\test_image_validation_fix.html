<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Upload Validation Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #45a049;
        }
        .test-button {
            background: #2196F3;
        }
        .test-button:hover {
            background: #1976D2;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .file-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Image Upload Validation Fix Test</h1>
        <p>Compare the behavior of product image upload (working) vs commercial register upload (fixed).</p>
        
        <div class="comparison">
            <!-- Product Upload Test -->
            <div class="test-section">
                <h3>✅ Product Image Upload (Reference - Working)</h3>
                <form id="productForm">
                    <div class="form-group">
                        <label for="productImage">Product Image:</label>
                        <input type="file" id="productImage" name="image" accept="image/*" required>
                        <div id="productFileInfo" class="file-info" style="display: none;"></div>
                    </div>
                    
                    <input type="hidden" name="user_id" value="1">
                    <input type="hidden" name="description" value="Test Product">
                    <input type="hidden" name="country_of_origin" value="Test Country">
                    <input type="hidden" name="price" value="100">
                    <input type="hidden" name="type" value="industrial">
                    
                    <button type="submit" class="test-button">🧪 Test Product Upload</button>
                </form>
                <div id="productResult"></div>
            </div>

            <!-- Commercial Register Upload Test -->
            <div class="test-section">
                <h3>🔧 Commercial Register Upload (Fixed)</h3>
                <form id="commercialForm">
                    <div class="form-group">
                        <label for="commercialImage">Commercial Register Image:</label>
                        <input type="file" id="commercialImage" name="commercial_register_photo" accept="image/*" required>
                        <div id="commercialFileInfo" class="file-info" style="display: none;"></div>
                    </div>
                    
                    <input type="hidden" name="full_name" value="Test Merchant">
                    <input type="hidden" name="email" id="merchantEmail" value="">
                    <input type="hidden" name="phone_number" value="+1234567890">
                    <input type="hidden" name="password" value="Test123!@#">
                    <input type="hidden" name="shipping_address" value="123 Test Street">
                    <input type="hidden" name="country" value="Test Country">
                    <input type="hidden" name="role" value="merchant">
                    
                    <button type="submit" class="test-button">🔧 Test Commercial Register Upload</button>
                </form>
                <div id="commercialResult"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 Test Results Comparison</h3>
            <p>Both uploads should now have consistent error handling and logging.</p>
            <button onclick="runBothTests()" class="test-button">🚀 Run Both Tests</button>
            <button onclick="clearAllResults()">🧹 Clear All Results</button>
            <div id="comparisonResult"></div>
        </div>
    </div>

    <script>
        // Auto-generate unique email
        document.getElementById('merchantEmail').value = `testmerchant${Date.now()}@example.com`;

        // File info display functions
        function setupFileInfo(inputId, infoId) {
            const input = document.getElementById(inputId);
            const info = document.getElementById(infoId);
            
            input.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    info.style.display = 'block';
                    info.innerHTML = `
                        <strong>File:</strong> ${file.name}<br>
                        <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                        <strong>Type:</strong> ${file.type}
                    `;
                } else {
                    info.style.display = 'none';
                }
            });
        }

        setupFileInfo('productImage', 'productFileInfo');
        setupFileInfo('commercialImage', 'commercialFileInfo');

        // Product upload test
        document.getElementById('productForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await testUpload(this, './api/products', 'productResult', 'Product Upload');
        });

        // Commercial register upload test
        document.getElementById('commercialForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await testUpload(this, './api/register', 'commercialResult', 'Commercial Register Upload');
        });

        async function testUpload(form, endpoint, resultId, testName) {
            const formData = new FormData(form);
            const resultDiv = document.getElementById(resultId);
            
            showResult(resultDiv, `Testing ${testName}...`, 'info');
            
            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                console.log(`${testName} Response:`, result);
                
                if (result.success) {
                    showResult(resultDiv, `✅ ${testName} SUCCESS!\n\nResponse: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(resultDiv, `❌ ${testName} FAILED!\n\nError: ${result.message}\n\nFull Response: ${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                console.error(`${testName} Error:`, error);
                showResult(resultDiv, `❌ ${testName} NETWORK ERROR!\n\nError: ${error.message}`, 'error');
            }
        }

        function showResult(element, message, type) {
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function runBothTests() {
            const productFile = document.getElementById('productImage').files[0];
            const commercialFile = document.getElementById('commercialImage').files[0];
            
            if (!productFile || !commercialFile) {
                showResult(document.getElementById('comparisonResult'), 
                    '❌ Please select images for both tests before running comparison.', 'error');
                return;
            }
            
            showResult(document.getElementById('comparisonResult'), 
                '🔄 Running both tests for comparison...', 'info');
            
            // Run both tests
            await testUpload(document.getElementById('productForm'), './api/products', 'productResult', 'Product Upload');
            await testUpload(document.getElementById('commercialForm'), './api/register', 'commercialResult', 'Commercial Register Upload');
            
            showResult(document.getElementById('comparisonResult'), 
                '✅ Both tests completed! Check the results above to compare error handling.', 'success');
        }

        function clearAllResults() {
            document.getElementById('productResult').innerHTML = '';
            document.getElementById('commercialResult').innerHTML = '';
            document.getElementById('comparisonResult').innerHTML = '';
        }
    </script>
</body>
</html>
