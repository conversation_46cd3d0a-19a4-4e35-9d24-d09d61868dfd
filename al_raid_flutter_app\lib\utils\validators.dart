import 'constants.dart';

class Validators {
  // Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  // Password validation
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < AppConstants.minPasswordLength) {
      return 'Password must be at least ${AppConstants.minPasswordLength} characters';
    }

    if (value.length > AppConstants.maxPasswordLength) {
      return 'Password must be less than ${AppConstants.maxPasswordLength} characters';
    }

    // Check for at least one number
    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }

    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'Password must contain at least one special character';
    }

    return null;
  }

  // Confirm password validation
  static String? validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }

    if (value != password) {
      return 'Passwords do not match';
    }

    return null;
  }

  // Name validation
  static String? validateName(String? value, [String fieldName = 'Name']) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }

    if (value.length < AppConstants.minNameLength) {
      return '$fieldName must be at least ${AppConstants.minNameLength} characters';
    }

    if (value.length > AppConstants.maxNameLength) {
      return '$fieldName must be less than ${AppConstants.maxNameLength} characters';
    }

    return null;
  }

  // Phone validation
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }

    // Saudi Arabia phone number format
    final phoneRegex = RegExp(r'^(05|5)[0-9]{8}$');
    if (!phoneRegex.hasMatch(value.replaceAll(' ', '').replaceAll('-', ''))) {
      return 'Please enter a valid Saudi phone number';
    }

    return null;
  }

  // Product name validation
  static String? validateProductName(String? value) {
    return validateName(value, 'Product name');
  }

  // Product description validation
  static String? validateProductDescription(String? value) {
    if (value == null || value.isEmpty) {
      return 'Product description is required';
    }

    if (value.length < 10) {
      return 'Description must be at least 10 characters';
    }

    if (value.length > 1000) {
      return 'Description must be less than 1000 characters';
    }

    return null;
  }

  // Product price validation
  static String? validateProductPrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'Product price is required';
    }

    final price = double.tryParse(value);
    if (price == null) {
      return 'Please enter a valid price';
    }

    if (price <= 0) {
      return 'Price must be greater than 0';
    }

    if (price > 1000000) {
      return 'Price must be less than 1,000,000';
    }

    return null;
  }

  // Required field validation
  static String? validateRequired(String? value, [String fieldName = 'Field']) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  // Category validation
  static String? validateCategory(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a category';
    }

    if (!AppConstants.productCategories.contains(value)) {
      return 'Please select a valid category';
    }

    return null;
  }

  // Address validation
  static String? validateAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Address is required';
    }

    if (value.length < 10) {
      return 'Please enter a complete address';
    }

    if (value.length > 500) {
      return 'Address is too long';
    }

    return null;
  }

  // Quantity validation
  static String? validateQuantity(String? value) {
    if (value == null || value.isEmpty) {
      return 'Quantity is required';
    }

    final quantity = int.tryParse(value);
    if (quantity == null) {
      return 'Please enter a valid quantity';
    }

    if (quantity <= 0) {
      return 'Quantity must be greater than 0';
    }

    if (quantity > 1000) {
      return 'Quantity must be less than 1000';
    }

    return null;
  }

  // Country validation
  static String? validateCountry(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a country';
    }

    if (!AppConstants.countries.contains(value)) {
      return 'Please select a valid country';
    }

    return null;
  }

  // Enhanced password validation with bilingual support
  static String? validatePasswordWithLocale(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'كلمة المرور مطلوبة' : 'Password is required';
    }

    if (value.length < AppConstants.minPasswordLength) {
      return isArabic
          ? 'كلمة المرور يجب أن تكون ${AppConstants.minPasswordLength} أحرف على الأقل'
          : 'Password must be at least ${AppConstants.minPasswordLength} characters';
    }

    if (value.length > AppConstants.maxPasswordLength) {
      return isArabic
          ? 'كلمة المرور يجب أن تكون أقل من ${AppConstants.maxPasswordLength} حرف'
          : 'Password must be less than ${AppConstants.maxPasswordLength} characters';
    }

    // Check for at least one number
    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return isArabic
          ? 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل'
          : 'Password must contain at least one number';
    }

    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return isArabic
          ? 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل'
          : 'Password must contain at least one special character';
    }

    return null;
  }
}
