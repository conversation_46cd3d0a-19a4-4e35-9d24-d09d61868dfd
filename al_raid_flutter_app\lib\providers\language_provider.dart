import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider with ChangeNotifier {
  Locale _locale = const Locale('en');
  String? _lastRoute;

  Locale get locale => _locale;
  String? get lastRoute => _lastRoute;

  bool get isArabic => _locale.languageCode == 'ar';
  bool get isEnglish => _locale.languageCode == 'en';

  LanguageProvider() {
    _loadLanguage();
  }

  void setLocale(Locale locale) {
    _locale = locale;
    notifyListeners();
    _saveLanguage(locale);
  }

  void setLastRoute(String route) {
    _lastRoute = route;
    _saveLastRoute(route);
  }

  void toggleLanguage() {
    if (_locale.languageCode == 'en') {
      setLocale(const Locale('ar'));
    } else {
      setLocale(const Locale('en'));
    }
  }

  Future<void> _saveLanguage(Locale locale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language_code', locale.languageCode);
  }

  Future<void> _saveLastRoute(String route) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('last_route', route);
  }

  Future<void> _loadLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('language_code') ?? 'en';
    _lastRoute = prefs.getString('last_route');
    _locale = Locale(languageCode);
    notifyListeners();
  }

  String getLocalizedText(String englishText, String arabicText) {
    return isArabic ? arabicText : englishText;
  }

  TextDirection get textDirection =>
      isArabic ? TextDirection.rtl : TextDirection.ltr;
}
