<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "<h2>Updating ALraid Database for User Approval System</h2>";
    
    // Check if status column exists in users table
    $stmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE 'status'");
    $stmt->execute();
    $statusColumnExists = $stmt->fetch() !== false;
    
    if (!$statusColumnExists) {
        // Add status column to users table
        $pdo->exec("ALTER TABLE users ADD COLUMN status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' AFTER role");
        echo "<p>✓ Status column added to users table</p>";
        
        // Update existing users to approved status (to maintain current functionality)
        $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE status = 'pending'");
        $stmt->execute();
        echo "<p>✓ Existing users set to approved status</p>";
    } else {
        echo "<p>✓ Status column already exists in users table</p>";
    }
    
    // Verify table structure
    echo "<h3>Updated Users Table Structure:</h3>";
    $stmt = $pdo->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show current users with their status
    echo "<h3>Current Users Status:</h3>";
    $stmt = $pdo->prepare("SELECT id, full_name, email, role, status, created_at FROM users ORDER BY created_at DESC");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Status</th><th>Created</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . ucfirst($user['role']) . "</td>";
            echo "<td><strong>" . ucfirst($user['status']) . "</strong></td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No users found in database.</p>";
    }
    
    echo "<h3>✅ User Approval System Update Complete!</h3>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>New user registrations will be set to 'pending' status</li>";
    echo "<li>Admin panel will show user approval functionality</li>";
    echo "<li>Login will check user approval status</li>";
    echo "<li>Users will receive appropriate status notifications</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
